import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Check } from 'lucide-react'

const SALE_END_DATE = new Date('2025-07-01T23:59:59').getTime()

const formSchema = z.object({
  name: z.string().min(2, 'Name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number is required'),
  recurring: z.boolean().optional()
})

type FormData = z.infer<typeof formSchema>

interface Package {
  name: string
  subtitle: string
  salePrice: string
  oldPrice?: string
  renewal?: string
  features: string[]
  monthly?: string
  cta: string
  highlight?: boolean
  new?: boolean
}

const packages: Package[] = [
  {
    name: 'Solo Hustler Bundle',
    subtitle: 'For individuals & side hustlers starting out',
    salePrice: '₦109,250',
    oldPrice: '₦125,000',
    monthly: '₦5,500',
    renewal: '₦45,000',
    features: [
      'Home, About, Services, Contact',
      'Mobile responsive',
      'WhatsApp chat button',
      'Fast loading',
      '1 Design revision',
      'Built with WordPress',
      'Free Logo Design',
      'Branded QR Code',
      '1 Business Email Account Included'
    ],
    cta: 'Start Now',
    highlight: false
  },
  {
    name: 'SME Kickstart Deal',
    subtitle: 'Ideal for small Nigerian businesses',
    salePrice: '₦166,750',
    oldPrice: '₦195,000',
    monthly: '₦7,500',
    renewal: '₦45,000',
    features: [
      'Pages: Home, About, Services, Gallery, Contact',
      'WhatsApp & Socials',
      'Google Map, Contact Form',
      'Basic SEO, Anti-spam',
      '2 Design revisions',
      'Free Logo Design',
      '2 Social Media Flyers',
      'Free Business Card Design (2 options)',
      'Branded QR Code',
      '3 Business Email Accounts Included'
    ],
    cta: 'Build My Website',
    highlight: false
  },
  {
    name: 'Business Growth Plus',
    subtitle: 'For service professionals & growing brands',
    salePrice: '₦247,250',
    oldPrice: '₦285,000',
    monthly: '₦10,000',
    renewal: '₦105,000',
    features: [
      'Slider, Banner, Blog setup',
      'Google Analytics Integration',
      'Standard SEO',
      '3 Design revisions',
      'Mobile Optimization',
      'Free Logo Design',
      '3 Social Media Designs (IG, FB, Twitter)',
      'Business Card Design (3 styles)',
      'Branded QR Code',
      '5 Business Email Accounts Included'
    ],
    cta: 'Let’s Grow',
    highlight: true
  },
  {
    name: 'Market Seller Pro Kit',
    subtitle: 'Tailored for product sellers & small shops',
    salePrice: '₦350,750',
    oldPrice: '₦405,000',
    monthly: '₦14,500',
    renewal: '₦105,000',
    features: [
      'Custom Homepage',
      'Product, Cart, Checkout Pages',
      '25 Product Uploads',
      'Payment Gateway Integration (Paystack, Flutterwave, Stripe, PayPal)',
      'WhatsApp + Google Analytics Setup',
      'Free Logo Design',
      '4 Social Media Posters',
      'Business Card Design (3 options)',
      'Branded QR Code',
      '10 Business Email Accounts Included'
    ],
    cta: 'Launch Store',
    highlight: false
  },
  {
    name: 'AI Smart Business Hub',
    subtitle: 'For brands needing voice AI & chat automation',
    salePrice: '₦437,000',
    oldPrice: '₦495,000',
    monthly: '₦17,000',
    renewal: '₦105,000+',
    features: [
      'Custom site + AI Voice Agent',
      '24/7 Customer Support Bot',
      'Virtual Assistant Capabilities',
      'Multilingual Support',
      '4 Revisions',
      '5 Social Media Posters',
      'Business Card Design',
      'Branded QR Code',
      'Free Logo Design',
      '15 Business Email Accounts Included'
    ],
    cta: 'Get AI Setup',
    highlight: false,
    new: true
  },
  {
    name: 'Big Shop Nation Plan',
    subtitle: 'Built for scale: big e-commerce & stores',
    salePrice: '₦580,750',
    oldPrice: '₦667,000',
    monthly: '₦24,000',
    renewal: '₦190,000',
    features: [
      'Unlimited Products',
      'CRM & In-store Credits',
      'Advanced Google Analytics',
      'Custom Design System',
      'Google Ads Ready',
      'Payment Gateway Integration (Paystack, Flutterwave, Stripe, PayPal)',
      '5 Social Media Designs',
      'Business Card Design',
      'Free Logo (SVG, PNG, JPG)',
      'Branded QR Code',
      '25 Business Email Accounts Included'
    ],
    cta: 'Scale My Store',
    highlight: false
  },
  {
    name: 'Custom Build & Corporate Plan',
    subtitle: 'Tailored for large firms & special systems',
    salePrice: 'Contact us',
    oldPrice: '',
    monthly: 'Flexible',
    renewal: 'Flexible',
    features: [
      'Everything in Big Shop +',
      'Advanced Integrations',
      'Intranet, Client Dashboards',
      'Marketing Funnels',
      'Dedicated Team Support',
      'Flexible Email/Hosting/CRM Setup'
    ],
    cta: 'Get a Quote',
    highlight: false
  }
]

export default function PricingSection() {
  const [now, setNow] = useState(Date.now())
  const [open, setOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<Package | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      recurring: false
    }
  })

  const onSubmit = (data: FormData) => {
    console.log('Form data:', data)
    window.open('https://paystack.shop/pay/ri42zx2eku', '_blank')
  }

  useEffect(() => {
    const interval = setInterval(() => setNow(Date.now()), 1000)
    return () => clearInterval(interval)
  }, [])

  const timeLeft = SALE_END_DATE - now
  const expired = timeLeft <= 0

  const countdown = expired
    ? null
    : `${Math.floor(timeLeft / (1000 * 60 * 60 * 24))}d ${Math.floor(
        (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )}h ${Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))}m`

  const setOpenForm = (pkg: Package) => {
    setSelectedPlan(pkg)
    setOpen(true)
  }

  return (
    <>
      <section className="section-padding bg-agency-dark relative overflow-hidden">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
              Our Pricing Plans
            </h2>
            <p className="text-agency-white-muted max-w-2xl mx-auto">
              Choose the perfect plan for your business needs. All plans include
              our premium support and satisfaction guarantee.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {packages.map((pkg, idx) => (
              <Card
                key={idx}
                className={`relative rounded-xl shadow-lg border transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 bg-agency-darker border-agency-white-muted/20 ${
                  pkg.highlight ? 'ring-2 ring-agency-green scale-105' : ''
                }`}
              >
                {/* {!expired && pkg.oldPrice && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-red-600 text-white text-xs font-semibold px-4 py-2 rounded-full animate-pulse shadow-lg">
                    🎉 Limited Time: {countdown}
                  </div>
                )} */}

                {pkg.highlight && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-agency-green text-agency-dark text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
                    Most Popular
                  </div>
                )}

                {pkg.new && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-agency-green text-agency-dark text-xs font-semibold px-4 py-2 rounded-full shadow-lg">
                    New Service
                  </div>
                )}

                <CardContent className="p-8 flex flex-col h-full">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {pkg.name}
                    </h3>
                    <p className="text-agency-white-muted mb-6">
                      {pkg.subtitle}
                    </p>

                    <div className="mb-4">
                      {pkg.oldPrice && !expired && (
                        <p className="text-lg line-through text-agency-rose-muted  font-semibold">
                          {pkg.oldPrice}
                        </p>
                      )}
                      <p className="text-4xl font-extrabold text-agency-green">
                        {pkg.salePrice}
                      </p>
                      {pkg.renewal && (
                        <p className="text-sm text-agency-white-muted mt-2">
                          Annual Renewal: {pkg.renewal}
                        </p>
                      )}
                    </div>
                  </div>

                  <ul className="space-y-4 mb-8 flex-grow">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="w-5 h-5 text-agency-green mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-agency-white-muted">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {pkg.monthly && (
                    <div className="mb-6 p-4 bg-agency-dark rounded-lg border border-agency-white-muted/10">
                      <p className="text-sm text-agency-white-muted">
                        <span className="text-agency-green font-semibold">
                          Optional:
                        </span>{' '}
                        <span className="font-semibold">
                          + {pkg.monthly}/month
                        </span>{' '}
                        for maintenance & updates
                      </p>
                    </div>
                  )}

                  <Button
                    className={`w-full py-6 text-lg font-semibold transition-all ${
                      pkg.highlight
                        ? 'bg-agency-green text-agency-dark hover:bg-agency-green/90 btn-glow'
                        : 'bg-transparent border-2 border-agency-green text-agency-green hover:bg-agency-green hover:text-agency-dark'
                    }`}
                    onClick={() => setOpenForm(pkg)}
                  >
                    {pkg.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="bg-agency-darker border-agency-white-muted/20">
          <DialogHeader>
            <DialogTitle className="text-white text-xl">
              Get Started with {selectedPlan?.name}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <Input
                placeholder="Your Full Name"
                {...register('name')}
                className="bg-agency-dark border-agency-white-muted/20 text-white placeholder:text-agency-white-muted"
              />
              {errors.name && (
                <p className="text-red-400 text-sm mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <Input
                placeholder="Your Email Address"
                {...register('email')}
                className="bg-agency-dark border-agency-white-muted/20 text-white placeholder:text-agency-white-muted"
              />
              {errors.email && (
                <p className="text-red-400 text-sm mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div>
              <Input
                placeholder="Phone Number"
                {...register('phone')}
                className="bg-agency-dark border-agency-white-muted/20 text-white placeholder:text-agency-white-muted"
              />
              {errors.phone && (
                <p className="text-red-400 text-sm mt-1">
                  {errors.phone.message}
                </p>
              )}
            </div>

            <div className="flex items-center gap-3">
              <Checkbox {...register('recurring')} />
              <label className="text-agency-white-muted">
                I want to subscribe to monthly maintenance
              </label>
            </div>

            <Button
              type="submit"
              className="w-full bg-agency-green text-agency-dark hover:bg-agency-green/90 py-6 text-lg font-semibold btn-glow"
            >
              Proceed to Payment
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
