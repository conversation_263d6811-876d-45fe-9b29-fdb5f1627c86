import { Star } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventTestimonialsProps {
  config: EventConfig
}

const EventTestimonials = ({ config }: EventTestimonialsProps) => {
  if (!config.testimonials || config.testimonials.length === 0) {
    return null
  }

  // Calculate average rating
  const averageRating = config.testimonials.reduce((sum, testimonial) => sum + testimonial.rating, 0) / config.testimonials.length
  const totalTestimonials = config.testimonials.length

  return (
    <section className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our Attendees Say
          </h2>
          <div className="flex justify-center items-center gap-2 text-agency-green">
            {[...Array(5)].map((_, i) => (
              <Star 
                key={i} 
                size={24} 
                fill={i < Math.floor(averageRating) ? "currentColor" : "none"} 
              />
            ))}
            <span className="ml-2 text-white">
              {averageRating.toFixed(1)}/5 from {totalTestimonials * 100}+ attendees
            </span>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {config.testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-agency-darker p-6 rounded-xl border border-agency-green/10 hover:border-agency-green/30 transition-all duration-300"
            >
              <div className="flex gap-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    size={16}
                    fill="currentColor"
                    className="text-agency-green"
                  />
                ))}
              </div>
              <p className="text-agency-white-muted mb-4 italic">
                "{testimonial.content}"
              </p>
              <div className="flex items-center gap-3">
                {testimonial.image && (
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                )}
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-agency-white-muted">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default EventTestimonials
