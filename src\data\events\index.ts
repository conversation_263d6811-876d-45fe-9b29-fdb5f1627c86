export { codeAndAIWorkshopConfig } from './code-and-ai-workshop'
export { techMeetupConfig } from './tech-meetup'

// Export all event configurations for easy access
import { codeAndAIWorkshopConfig } from './code-and-ai-workshop'
import { techMeetupConfig } from './tech-meetup'
import { EventConfig } from '@/types/event'

export const eventConfigs: Record<string, EventConfig> = {
  'code-and-ai-workshop': codeAndAIWorkshopConfig,
  'tech-meetup': techMeetupConfig,
  // Add more events here as needed
}

// Helper function to get event config by ID
export const getEventConfig = (eventId: string): EventConfig | null => {
  return eventConfigs[eventId] || null
}
