import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { Clock, Calendar, MapPin, ArrowRight } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventHeroProps {
  config: EventConfig
  timeLeft: {
    days: number
    hours: number
    minutes: number
    seconds: number
  }
  onRegisterClick: () => void
}

const EventHero = ({ config, timeLeft, onRegisterClick }: EventHeroProps) => {
  const heroRef = useRef<HTMLDivElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const tl = gsap.timeline()

    tl.fromTo(
      heroRef.current?.children,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'power3.out' }
    )

    // Floating CTA animation
    if (ctaRef.current) {
      gsap.to(ctaRef.current, {
        y: -10,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut'
      })
    }

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 pt-24">
      <div className="absolute inset-0 bg-gradient-overlay"></div>

      <div
        ref={heroRef}
        className="relative z-10 max-w-4xl mx-auto text-center"
      >
        {/* Urgency Banner */}
        <div className="inline-flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
          <Clock size={16} />
          <span>{config.urgencyMessage}</span>
        </div>

        {/* Main Headline */}
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
          {config.title.split(' ').map((word, index) => (
            <span key={index} className={index === config.title.split(' ').length - 1 ? 'text-gradient block' : ''}>
              {word}{' '}
            </span>
          ))}
        </h1>

        {/* Subheadline */}
        <p className="text-xl md:text-2xl text-agency-white-muted mb-8 max-w-3xl mx-auto">
          {config.subtitle}
        </p>

        {/* Event Details */}
        <div className="flex flex-wrap justify-center gap-6 mb-8 text-agency-white-muted">
          <div className="flex items-center gap-2">
            <Calendar size={20} className="text-agency-green" />
            <span>{config.details.date}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock size={20} className="text-agency-green" />
            <span>{config.details.time}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin size={20} className="text-agency-green" />
            <span>{config.details.location}</span>
          </div>
        </div>

        {/* Countdown Timer */}
        {config.features.countdown && (
          <div className="grid grid-cols-4 gap-4 max-w-md mx-auto mb-8">
            {Object.entries(timeLeft).map(([unit, value]) => (
              <div
                key={unit}
                className="bg-agency-darker rounded-lg p-4 border border-agency-green/20"
              >
                <div className="text-2xl md:text-3xl font-bold text-agency-green">
                  {value}
                </div>
                <div className="text-sm text-agency-white-muted capitalize">
                  {unit}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Primary CTA */}
        <div ref={ctaRef} className="space-y-4">
          <button
            onClick={onRegisterClick}
            className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2"
          >
            {config.pricing.earlyBird.price === 0 ? 'Register for Free' : 'Secure Your Spot Now'}
            <ArrowRight size={20} />
          </button>
          
          {config.features.pricing && (
            <p className="text-sm text-agency-white-muted">
              {config.spotsRemaining && (
                <>⚡ Only {config.spotsRemaining} spots remaining • </>
              )}
              {config.pricing.earlyBird.price > 0 && (
                <>💰 Early Bird: {config.pricing.earlyBird.currency}{config.pricing.earlyBird.price.toLocaleString()} (Regular: {config.pricing.regular.currency}{config.pricing.regular.price.toLocaleString()})</>
              )}
            </p>
          )}
        </div>
      </div>
    </section>
  )
}

export default EventHero
