import { Shield } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventHeaderProps {
  config: EventConfig
}

const EventHeader = ({ config }: EventHeaderProps) => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-agency-dark/95 backdrop-blur-sm border-b border-agency-green/10">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        {/* Logo */}
        <div className="flex items-center">
          <img src="/logo-01.svg" alt="KavaraDigital" className="w-40 h-10" />
        </div>

        {/* Trust Indicators & Contact */}
        <div className="hidden md:flex items-center gap-6 text-sm">
          <div className="flex items-center gap-2 text-agency-white-muted">
            <Shield size={16} className="text-agency-green" />
            <span>Secure Registration</span>
          </div>
          <div className="text-agency-white-muted">
            Questions?{' '}
            <a
              href={`tel:${config.contact.phone}`}
              className="text-agency-green hover:underline font-medium"
            >
              {config.contact.phone.replace('+234', '0').replace('+', '')}
            </a>
          </div>
        </div>

        {/* Mobile Contact */}
        <div className="md:hidden">
          <a
            href={`tel:${config.contact.phone}`}
            className="text-agency-green hover:underline text-sm font-medium"
          >
            Call Now
          </a>
        </div>
      </div>
    </header>
  )
}

export default EventHeader
