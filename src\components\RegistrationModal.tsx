import React, { useEffect, useRef } from 'react'
import { X, ArrowRight } from 'lucide-react'
import { gsap } from 'gsap'

interface RegistrationModalProps {
  isOpen: boolean
  onClose: () => void
  eventTitle?: string
  registrationUrl?: string
}

const RegistrationModal: React.FC<RegistrationModalProps> = ({
  isOpen,
  onClose,
  eventTitle = 'AI & Code Workshop',
  registrationUrl = 'https://forms.gle/T7gPtiTs1RJiK5E76'
}) => {
  const modalRef = useRef<HTMLDivElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // Use the provided registration URL
  const formUrl = registrationUrl

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'

      // Animate modal in
      if (overlayRef.current && contentRef.current) {
        gsap.set(overlayRef.current, { opacity: 0 })
        gsap.set(contentRef.current, { scale: 0.8, opacity: 0 })

        gsap.to(overlayRef.current, { opacity: 1, duration: 0.3 })
        gsap.to(contentRef.current, {
          scale: 1,
          opacity: 1,
          duration: 0.4,
          ease: 'back.out(1.7)',
          delay: 0.1
        })
      }
    } else {
      // Restore body scroll
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleClose = () => {
    if (overlayRef.current && contentRef.current) {
      gsap.to(contentRef.current, {
        scale: 0.8,
        opacity: 0,
        duration: 0.2
      })
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        onComplete: onClose
      })
    } else {
      onClose()
    }
  }

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  const handleEscapeKey = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey)
      return () => document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
    >
      {/* Overlay */}
      <div
        ref={overlayRef}
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={handleOverlayClick}
      />

      {/* Modal Content */}
      <div
        ref={contentRef}
        className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-agency-dark text-white">
          <div>
            <h2 className="text-2xl font-bold">Register for {eventTitle}</h2>
            <p className="text-agency-white-muted mt-1">
              Secure your spot - Limited seats available!
            </p>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-agency-green/10 rounded-full transition-colors"
            aria-label="Close modal"
          >
            <X size={24} className="text-agency-white-muted hover:text-white" />
          </button>
        </div>

        {/* Form Container */}
        <div className="relative h-[600px] md:h-[700px]">
          {/* Option 1: Try iframe first */}
          <div className="h-full">
            <iframe
              src={formUrl}
              className="w-full h-full border-0"
              title={`${eventTitle} Registration`}
              onError={() => {
                // If iframe fails, show fallback
                console.log('Iframe failed to load, showing fallback')
              }}
            >
              {/* Fallback content if iframe doesn't work */}
              <div className="flex items-center justify-center h-full bg-gray-50">
                <div className="text-center p-8">
                  <h3 className="text-xl font-bold mb-4">
                    Register for {eventTitle}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Click the button below to open the registration form in a
                    new tab.
                  </p>
                  <a
                    href={formUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-agency-green text-white px-6 py-3 rounded-lg font-bold hover:bg-opacity-90 transition-all"
                  >
                    Open Registration Form
                    <ArrowRight size={20} />
                  </a>
                </div>
              </div>
            </iframe>
          </div>

          {/* Fallback button overlay (shows if iframe has issues) */}
          <div className="absolute bottom-4 right-4">
            <a
              href={formUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-agency-dark text-white px-4 py-2 rounded-lg text-sm hover:bg-opacity-90 transition-all flex items-center gap-2"
            >
              Open in New Tab
              <ArrowRight size={16} />
            </a>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>🔒 Secure Registration</span>
              <span>📞 Need help? Call 0802-902-0121</span>
            </div>
            <button
              onClick={handleClose}
              className="text-agency-green hover:text-agency-green/80 font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RegistrationModal
