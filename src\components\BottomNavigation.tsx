import React, { useEffect, useRef, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { gsap } from 'gsap'
import {
  Home,
  Briefcase,
  DollarSign,
  GitBranch,
  FolderOpen,
  Users,
  MessageCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

const BottomNavigation = () => {
  const location = useLocation()
  const navRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.9)

  const navItems = [
    { name: 'Home', path: '/', icon: Home },
    { name: 'Services', path: '/services', icon: Briefcase },
    { name: 'Pricing', path: '/pricing', icon: DollarSign },
    { name: 'Process', path: '/process', icon: GitBranch },
    { name: 'Work', path: '/work', icon: FolderOpen },
    { name: 'About', path: '/about', icon: Users },
    { name: 'Contact', path: '/contact', icon: MessageCircle }
  ]

  const isActive = (path: string) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  useEffect(() => {
    // Initial animation when component mounts
    if (navRef.current) {
      gsap.fromTo(
        navRef.current,
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out' }
      )
    }
  }, [])

  useEffect(() => {
    // Auto-scroll to active item
    const activeIndex = navItems.findIndex((item) => isActive(item.path))
    if (activeIndex !== -1 && scrollContainerRef.current) {
      const container = scrollContainerRef.current
      const activeItem = container.children[activeIndex] as HTMLElement

      if (activeItem) {
        const containerWidth = container.offsetWidth
        const itemLeft = activeItem.offsetLeft
        const itemWidth = activeItem.offsetWidth
        const scrollLeft = itemLeft - containerWidth / 2 + itemWidth / 2

        gsap.to(container, {
          scrollLeft: Math.max(0, scrollLeft),
          duration: 0.4,
          ease: 'power2.out'
        })
      }
    }
  }, [location.pathname])

  useEffect(() => {
    // Handle scroll-based background fade
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight

      // Calculate how close we are to the bottom (in pixels)
      const distanceFromBottom = documentHeight - (scrollTop + windowHeight)

      // Start fading when we're within 300px of the bottom
      const fadeStartDistance = 300

      if (distanceFromBottom <= fadeStartDistance) {
        // Calculate opacity based on distance from bottom
        // When distanceFromBottom = fadeStartDistance, opacity = 0.9
        // When distanceFromBottom = 0, opacity = 0
        const bgOpacity = Math.max(
          0,
          (distanceFromBottom / fadeStartDistance) * 0.9
        )
        setBackgroundOpacity(bgOpacity)
      } else {
        // Reset to full opacity when not near bottom
        setBackgroundOpacity(0.9)
      }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Initial check
    handleScroll()

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const handleItemClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    const target = event.currentTarget

    // Add click animation
    gsap.to(target, {
      scale: 0.9,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut'
    })
  }

  return (
    <div className="md:hidden fixed bottom-4 left-4 right-4 z-50 flex justify-center">
      <nav
        ref={navRef}
        className="rounded-full px-2 py-2 max-w-full backdrop-blur-xl transition-all duration-300"
        style={{
          backgroundColor: `rgba(35, 20, 43, ${backgroundOpacity})`,
          borderColor: `rgba(147, 96, 147, ${backgroundOpacity * 0.1})`,
          boxShadow: `0 25px 50px -12px rgba(6, 3, 9, ${
            backgroundOpacity * 0.6
          })`,
          border: '1px solid'
        }}
      >
        <div
          ref={scrollContainerRef}
          className="flex items-center gap-1 overflow-x-auto scroll-smooth scrollbar-hide"
        >
          {navItems.map((item) => {
            const Icon = item.icon
            const isItemActive = isActive(item.path)

            return (
              <Link
                key={item.name}
                to={item.path}
                onClick={handleItemClick}
                className={cn(
                  'flex items-center gap-2 px-4 py-2.5 rounded-full transition-all duration-300 whitespace-nowrap min-w-fit active:scale-95 relative',
                  isItemActive
                    ? 'bg-agency-green text-agency-dark shadow-lg shadow-agency-green/25 font-semibold'
                    : 'text-agency-white-muted hover:text-white hover:bg-agency-green/10 hover:shadow-md'
                )}
              >
                <Icon
                  size={18}
                  className={cn(
                    'transition-all duration-200',
                    isItemActive ? 'scale-110' : 'scale-100'
                  )}
                />
                <span
                  className={cn(
                    'text-sm font-medium transition-all duration-200',
                    isItemActive
                      ? 'text-agency-dark font-semibold'
                      : 'text-agency-white-muted'
                  )}
                >
                  {item.name}
                </span>
              </Link>
            )
          })}
        </div>
      </nav>
    </div>
  )
}

export default BottomNavigation
