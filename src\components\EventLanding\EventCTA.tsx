import { ArrowRight } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventCTAProps {
  config: EventConfig
  timeLeft: {
    days: number
    hours: number
    minutes: number
    seconds: number
  }
  onRegisterClick: () => void
}

const EventCTA = ({ config, timeLeft, onRegisterClick }: EventCTAProps) => {
  const isFree = config.pricing.earlyBird.price === 0 && config.pricing.regular.price === 0

  return (
    <section className="py-16 px-4">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-gradient-to-r from-agency-green/10 to-agency-green/5 p-8 md:p-12 rounded-2xl border border-agency-green/20">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Don't Miss Out!
          </h2>
          <p className="text-xl text-agency-white-muted mb-8 max-w-2xl mx-auto">
            {config.spotsRemaining && config.totalSpots ? (
              <>Only {config.spotsRemaining} out of {config.totalSpots} spots remaining. </>
            ) : (
              'Limited spots available. '
            )}
            Secure your place today and transform your {config.type === 'workshop' ? 'skills' : 'knowledge'}.
          </p>

          <div className="space-y-6">
            <button
              onClick={onRegisterClick}
              className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2"
            >
              {isFree ? 'Register for Free Now' : 'Secure Your Spot Now'}
              <ArrowRight size={20} />
            </button>

            {config.features.countdown && (
              <p className="text-sm text-agency-white-muted">
                ⏰ {config.pricing.earlyBird.deadline ? 'Early Bird pricing ends' : 'Event starts'} in {timeLeft.days} days, {timeLeft.hours} hours
              </p>
            )}
          </div>

          <div className="mt-8 pt-8 border-t border-agency-green/10 text-center text-agency-white-muted">
            <p>
              Questions? Email us at{' '}
              <a
                href={`mailto:${config.contact.email}`}
                className="text-agency-green hover:underline"
              >
                {config.contact.email}
              </a>
            </p>
            <p>
              or call{' '}
              <a
                href={`tel:${config.contact.phone}`}
                className="text-agency-green hover:underline"
              >
                {config.contact.phone.replace('+234', '0').replace('+', '')}
              </a>
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EventCTA
