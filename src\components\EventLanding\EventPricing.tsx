import { ArrowR<PERSON>, CheckCircle, Shield } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventPricingProps {
  config: EventConfig
  onRegisterClick: () => void
}

const EventPricing = ({ config, onRegisterClick }: EventPricingProps) => {
  const { pricing } = config
  const isFree = pricing.earlyBird.price === 0 && pricing.regular.price === 0
  const hasEarlyBird = pricing.earlyBird.price !== pricing.regular.price

  if (isFree) {
    return (
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-agency-darker p-8 rounded-xl border border-agency-green/20">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Free Event
            </h2>
            <p className="text-agency-white-muted text-lg mb-6">
              Join us for this complimentary event - no cost, just value!
            </p>
            <button
              onClick={onRegisterClick}
              className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2"
            >
              Register for Free
              <ArrowRight size={20} />
            </button>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Investment in Your Future
          </h2>
          <p className="text-agency-white-muted text-lg">
            Choose the option that works best for you
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Early Bird Pricing */}
          {hasEarlyBird && (
            <div className="bg-agency-darker p-8 rounded-xl border-2 border-agency-green relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-agency-green text-agency-dark px-4 py-2 rounded-full text-sm font-bold">
                  EARLY BIRD
                </span>
              </div>
              
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-agency-green mb-2">
                  {pricing.earlyBird.currency}{pricing.earlyBird.price.toLocaleString()}
                </div>
                {pricing.earlyBird.deadline && (
                  <p className="text-agency-white-muted text-sm">
                    Until {pricing.earlyBird.deadline}
                  </p>
                )}
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Full workshop access</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>All materials included</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Certificate of completion</span>
                </div>
                {config.features.guarantee && (
                  <div className="flex items-center gap-2">
                    <Shield size={16} className="text-agency-green" />
                    <span>Money-back guarantee</span>
                  </div>
                )}
              </div>

              <button
                onClick={onRegisterClick}
                className="w-full bg-agency-green text-agency-dark py-3 rounded-lg font-bold hover:bg-opacity-90 transition-all"
              >
                Get Early Bird Price
              </button>
            </div>
          )}

          {/* Regular Pricing */}
          <div className="bg-agency-darker p-8 rounded-xl border border-agency-green/20">
            <div className="text-center mb-6">
              <div className="text-4xl font-bold text-white mb-2">
                {pricing.regular.currency}{pricing.regular.price.toLocaleString()}
              </div>
              <p className="text-agency-white-muted text-sm">
                Regular Price
              </p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-agency-green" />
                <span>Full workshop access</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-agency-green" />
                <span>All materials included</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-agency-green" />
                <span>Certificate of completion</span>
              </div>
              {config.features.guarantee && (
                <div className="flex items-center gap-2">
                  <Shield size={16} className="text-agency-green" />
                  <span>Money-back guarantee</span>
                </div>
              )}
            </div>

            <button
              onClick={onRegisterClick}
              className="w-full bg-agency-green/20 text-agency-green border border-agency-green py-3 rounded-lg font-bold hover:bg-agency-green hover:text-agency-dark transition-all"
            >
              Register Now
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EventPricing
