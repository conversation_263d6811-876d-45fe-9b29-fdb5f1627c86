import { Trophy, Zap, Users, Shield, CheckCircle } from 'lucide-react'
import { EventConfig } from '@/types/event'

interface EventBenefitsProps {
  config: EventConfig
}

// Icon mapping for string-based icon names
const iconMap = {
  Trophy,
  Zap,
  Users,
  Shield,
  CheckCircle
}

const EventBenefits = ({ config }: EventBenefitsProps) => {
  return (
    <section className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What You'll Get
          </h2>
          <p className="text-agency-white-muted text-lg max-w-2xl mx-auto">
            {config.description}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {config.benefits.map((benefit, index) => {
            const IconComponent = iconMap[benefit.icon as keyof typeof iconMap] || CheckCircle
            
            return (
              <div
                key={index}
                className="text-center p-6 rounded-xl bg-agency-darker border border-agency-green/10 hover:border-agency-green/30 transition-all duration-300"
              >
                <div className="w-16 h-16 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <IconComponent size={32} className="text-agency-green" />
                </div>
                <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
                <p className="text-agency-white-muted">{benefit.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default EventBenefits
